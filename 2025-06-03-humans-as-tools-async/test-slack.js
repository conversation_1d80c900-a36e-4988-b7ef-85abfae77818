#!/usr/bin/env node

// Test script to send a Slack message through <PERSON><PERSON>ayer
require('dotenv').config();
const { humanlayer } = require('humanlayer');

async function testSlackMessage() {
    console.log("💬 Testing Slack message through HumanLayer...");
    
    try {
        // Initialize HumanLayer with Slack channel
        const hl = humanlayer({
            apiKey: process.env.HUMANLAYER_API_KEY,
            runId: `slack-test-${Date.now()}`,
            contactChannel: {
                slack: {
                    channel_or_user_id: process.env.CLAIMS_SLACK_CHANNEL || 'C1234567890',
                    context_about_channel_or_user: "claims processing test channel"
                }
            }
        });

        console.log(`📤 Sending test message to Slack channel: ${process.env.CLAIMS_SLACK_CHANNEL || 'C1234567890'}`);

        // Send a test approval request via Slack
        const response = await hl.createHumanContact({
            spec: {
                msg: "🧪 **Test Slack Message from Claims System**\n\nThis is a test message to verify that the claims processing system can send Slack notifications successfully.\n\n**Test Details:**\n- System: Claims Processing\n- Purpose: Slack integration verification\n- Time: " + new Date().toISOString() + "\n\nPlease approve this test to confirm Slack integration is working! 🚀",
                state: {
                    test_id: `slack-test-${Date.now()}`,
                    purpose: "slack_integration_verification",
                    channel: process.env.CLAIMS_SLACK_CHANNEL || 'C1234567890'
                }
            }
        });

        console.log("✅ Slack message sent successfully!");
        console.log("📋 Response details:", {
            status: response.status,
            approved: response.approved,
            comment: response.comment
        });

        if (response.approved) {
            console.log("🎉 Slack integration confirmed - message received and approved!");
        } else if (response.approved === false) {
            console.log("❌ Message was rejected");
        } else {
            console.log("⏳ Message sent, waiting for response...");
        }

    } catch (error) {
        console.error("❌ Error sending Slack message:", error);
        
        if (error.message.includes('API key')) {
            console.log("💡 Check your HUMANLAYER_API_KEY in .env file");
        } else if (error.message.includes('channel')) {
            console.log("💡 Check your CLAIMS_SLACK_CHANNEL in .env file");
            console.log("💡 Make sure the channel ID is correct (starts with C)");
        } else if (error.message.includes('bot')) {
            console.log("💡 Check your HUMANLAYER_SLACK_BOT_TOKEN in .env file");
            console.log("💡 Make sure the bot is added to the channel");
        }
    }
}

// Run the test
testSlackMessage().catch(console.error);
