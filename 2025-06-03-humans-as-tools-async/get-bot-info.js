#!/usr/bin/env node

// <PERSON>ript to get bot information
require('dotenv').config();

async function getBotInfo() {
    const token = process.env.HUMANLAYER_SLACK_BOT_TOKEN;
    
    if (!token) {
        console.error("❌ HUMANLAYER_SLACK_BOT_TOKEN not found in .env file");
        return;
    }

    console.log("🤖 Getting bot information...");

    try {
        const response = await fetch('https://slack.com/api/auth.test', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (!data.ok) {
            console.error("❌ Slack API error:", data.error);
            return;
        }

        console.log("🎯 Bot Details:");
        console.log(`   Bot Name: @${data.user}`);
        console.log(`   Bot ID: ${data.user_id}`);
        console.log(`   Team: ${data.team}`);
        console.log(`   URL: ${data.url}`);
        console.log("");
        console.log("📝 To add the bot to a channel:");
        console.log(`   1. Go to the #test channel in Slack`);
        console.log(`   2. Type: /invite @${data.user}`);
        console.log(`   3. Or use channel settings → Add apps`);
        console.log("");
        console.log("🔧 Current channel configuration:");
        console.log(`   CLAIMS_SLACK_CHANNEL=${process.env.CLAIMS_SLACK_CHANNEL}`);
        console.log(`   CLAIMS_TEAM_SLACK_CHANNEL=${process.env.CLAIMS_TEAM_SLACK_CHANNEL}`);

    } catch (error) {
        console.error("❌ Error getting bot info:", error.message);
    }
}

getBotInfo();
