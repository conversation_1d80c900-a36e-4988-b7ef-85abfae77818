# HumanLayer Email & Slack Integration Setup

This guide will help you set up HumanLayer to receive approval requests via email or Slack when your agent needs human input.

## 🔑 Step 1: Get HumanLayer API Key

1. Go to [app.humanlayer.dev](https://app.humanlayer.dev)
2. Sign up/login to your account
3. Navigate to your project settings
4. Copy your API key
5. Add it to your `.env` file:
   ```
   HUMANLAYER_API_KEY=your_humanlayer_api_key_here
   ```

## 📧 Step 2: Email Integration (Recommended)

### Simple Email Setup
1. Add your email to `.env`:
   ```
   HUMANLAYER_EMAIL_ADDRESS=<EMAIL>
   ```

2. That's it! When the agent needs approval, you'll receive emails with:
   - **Approve** button (green)
   - **Reject** button (red)
   - Context about what needs approval

### How Email Integration Works
- Agent sends approval request → HumanLayer → Email to you
- You click Approve/Reject in the email
- Response goes back to agent → Agent continues/stops

### Email Features
- **Threading**: Replies stay in the same email thread
- **Custom templates**: Customize the email appearance
- **Rich context**: Emails include full details of what needs approval

## 💬 Step 3: Slack Integration (Alternative)

### Option A: Use HumanLayer Slack App (Easiest)
1. Go to [HumanLayer Dashboard](https://app.humanlayer.dev)
2. Navigate to "Integrations"
3. Click "HumanLayer App" under Slack section
4. Follow the Slack approval workflow
5. Done! Approvals will come to your Slack DMs

### Option B: Use Your Own Slack App (Advanced)
For custom branding and enterprise control:

1. **Create Slack App**:
   - Go to [api.slack.com](https://api.slack.com/quickstart)
   - Create new app with this manifest:
   ```json
   {
     "display_information": {
       "name": "My Support Agent",
       "description": "AI Agent with Human Approvals"
     },
     "features": {
       "bot_user": {
         "display_name": "Support Agent",
         "always_online": false
       }
     },
     "oauth_config": {
       "scopes": {
         "bot": [
           "chat:write", "channels:read", "im:write", 
           "im:read", "users:read", "app_mentions:read"
         ]
       }
     },
     "settings": {
       "event_subscriptions": {
         "request_url": "https://api.humanlayer.dev/humanlayer/v1/slack/events",
         "bot_events": ["message.im", "app_mention"]
       },
       "interactivity": {
         "is_enabled": true,
         "request_url": "https://api.humanlayer.dev/humanlayer/v1/slack/interactions"
       }
     }
   }
   ```

2. **Configure in HumanLayer**:
   - Dashboard → Integrations → "Use Your Own" under Slack
   - Enter Signing Secret and App ID
   - Save configuration

3. **Add to .env**:
   ```
   HUMANLAYER_SLACK_BOT_TOKEN=xoxb-your-bot-token
   HUMANLAYER_SLACK_CHANNEL_ID=C1234567890  # Channel ID or leave blank for DM
   ```

### Getting Slack IDs
- **Channel ID**: Right-click channel → "Channel Details" → Copy ID at bottom
- **User ID**: Right-click user → "View Profile" → More → Copy member ID

## 🧪 Step 4: Test Your Setup

1. **Copy environment file**:
   ```bash
   cp .env.example .env
   ```

2. **Edit .env with your credentials**:
   ```bash
   # Required
   OPENAI_API_KEY=sk-...
   HUMANLAYER_API_KEY=hl-...
   
   # Choose one:
   HUMANLAYER_EMAIL_ADDRESS=<EMAIL>
   # OR
   HUMANLAYER_SLACK_CHANNEL_ID=C1234567890
   ```

3. **Test with division (requires approval)**:
   ```bash
   npm run dev
   # Type: "divide 10 by 2"
   ```

4. **Check your email/Slack for approval request**

## 🔄 How It Works

### Email Flow
```
User: "divide 10 by 2"
↓
Agent: Needs approval for division
↓
HumanLayer: Sends email to you
↓
You: Click "Approve" in email
↓
Agent: Receives approval, performs division
↓
Agent: "The result is 5"
```

### Slack Flow
```
User: "process refund for order 12345"
↓
Agent: Needs approval for refund
↓
HumanLayer: Sends Slack message with buttons
↓
You: Click "Approve" button in Slack
↓
Agent: Processes refund
↓
Agent: "Refund processed successfully"
```

## 🎯 What Gets Sent for Approval

### Division Operations
- **Function**: `divide`
- **Parameters**: `a=10, b=2`
- **Reason**: Division requires human oversight

### Refund Processing
- **Function**: `process_refund`
- **Parameters**: `order_id=12345, amount=50, reason="damaged item"`
- **Reason**: Financial operations need approval

### Clarification Requests
- **Message**: "Could you clarify the second number?"
- **Context**: When agent doesn't understand input

## 🛠️ Troubleshooting

### Email Issues
- **Not receiving emails**: Check spam folder
- **Buttons not working**: Make sure you're clicking the actual buttons, not replying
- **Wrong email**: Update `HUMANLAYER_EMAIL_ADDRESS` in `.env`

### Slack Issues
- **Bot not responding**: Check bot token and permissions
- **Wrong channel**: Verify `HUMANLAYER_SLACK_CHANNEL_ID` is correct
- **Permission errors**: Ensure bot has `chat:write` and `im:write` scopes

### General Issues
- **API key errors**: Verify `HUMANLAYER_API_KEY` is correct
- **No approvals**: Check that you're testing operations that require approval (divide, refunds)

## 🚀 Next Steps

1. **Test basic approval flow** with division
2. **Try refund processing** with approval
3. **Customize email templates** (optional)
4. **Set up multiple approvers** for different operations
5. **Add webhook integration** for advanced workflows

## 📚 Resources

- [HumanLayer Documentation](https://humanlayer.dev/docs)
- [Email Templates Example](https://github.com/humanlayer/humanlayer/tree/main/examples/ts_email_templates)
- [Slack Integration Example](https://github.com/humanlayer/humanlayer/tree/main/examples/langchain)
- [HumanLayer Dashboard](https://app.humanlayer.dev)
