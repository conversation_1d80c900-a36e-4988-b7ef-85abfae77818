import { ClaimData } from '../claims/types';

export interface SlackNotificationOptions {
    channel: string;
    claimData: ClaimData;
    notificationType: 'approval_request' | 'claim_update' | 'claim_approved' | 'claim_rejected';
    customerEmail?: string;
}

/**
 * Send direct Slack notification using Slack API
 */
export async function sendSlackNotification(options: SlackNotificationOptions): Promise<void> {
    const token = process.env.HUMANLAYER_SLACK_BOT_TOKEN;
    
    if (!token) {
        console.warn('⚠️ No Slack bot token configured, skipping Slack notification');
        return;
    }

    try {
        console.log(`📤 Sending Slack notification to channel: ${options.channel}`);

        const message = createSlackMessage(options);

        const response = await fetch('https://slack.com/api/chat.postMessage', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(message)
        });

        const data = await response.json();

        if (data.ok) {
            console.log('✅ Slack notification sent successfully');
        } else {
            console.error('❌ Slack API error:', data.error);
        }

    } catch (error) {
        console.error('❌ Error sending Slack notification:', error);
    }
}

/**
 * Create Slack message based on notification type
 */
function createSlackMessage(options: SlackNotificationOptions) {
    const { claimData, notificationType, customerEmail } = options;
    
    const baseMessage = {
        channel: options.channel,
        username: 'Claims Processing System',
        icon_emoji: ':hospital:'
    };

    switch (notificationType) {
        case 'approval_request':
            return {
                ...baseMessage,
                text: `🚨 Claim Approval Required - ${claimData.claimId}`,
                blocks: [
                    {
                        type: "header",
                        text: {
                            type: "plain_text",
                            text: "🚨 Claim Approval Required"
                        }
                    },
                    {
                        type: "section",
                        fields: [
                            {
                                type: "mrkdwn",
                                text: `*Claim ID:*\n${claimData.claimId}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Amount:*\n$${claimData.amount.toLocaleString()}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Customer:*\n${customerEmail || 'Unknown'}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Policy:*\n${claimData.policyNumber || 'N/A'}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Type:*\n${claimData.claimType || 'Unknown'}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Date:*\n${claimData.incidentDate || 'N/A'}`
                            }
                        ]
                    },
                    {
                        type: "section",
                        text: {
                            type: "mrkdwn",
                            text: `*Description:*\n${claimData.description || 'No description provided'}`
                        }
                    },
                    {
                        type: "actions",
                        elements: [
                            {
                                type: "button",
                                text: {
                                    type: "plain_text",
                                    text: "✅ Approve Claim"
                                },
                                style: "primary",
                                value: `approve_${claimData.claimId}`,
                                action_id: "approve_claim"
                            },
                            {
                                type: "button",
                                text: {
                                    type: "plain_text",
                                    text: "❌ Reject Claim"
                                },
                                style: "danger",
                                value: `reject_${claimData.claimId}`,
                                action_id: "reject_claim"
                            },
                            {
                                type: "button",
                                text: {
                                    type: "plain_text",
                                text: "📝 Request Info"
                                },
                                value: `info_${claimData.claimId}`,
                                action_id: "request_info"
                            }
                        ]
                    },
                    {
                        type: "context",
                        elements: [
                            {
                                type: "mrkdwn",
                                text: `⏰ Submitted: ${new Date().toLocaleString()} | 🔗 Claim ID: ${claimData.claimId}`
                            }
                        ]
                    }
                ]
            };

        case 'claim_approved':
            return {
                ...baseMessage,
                text: `✅ Claim Approved - ${claimData.claimId}`,
                blocks: [
                    {
                        type: "header",
                        text: {
                            type: "plain_text",
                            text: "✅ Claim Approved"
                        }
                    },
                    {
                        type: "section",
                        fields: [
                            {
                                type: "mrkdwn",
                                text: `*Claim ID:*\n${claimData.claimId}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Amount:*\n$${claimData.amount.toLocaleString()}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Customer:*\n${customerEmail || 'Unknown'}`
                            }
                        ]
                    }
                ]
            };

        case 'claim_rejected':
            return {
                ...baseMessage,
                text: `❌ Claim Rejected - ${claimData.claimId}`,
                blocks: [
                    {
                        type: "header",
                        text: {
                            type: "plain_text",
                            text: "❌ Claim Rejected"
                        }
                    },
                    {
                        type: "section",
                        fields: [
                            {
                                type: "mrkdwn",
                                text: `*Claim ID:*\n${claimData.claimId}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Amount:*\n$${claimData.amount.toLocaleString()}`
                            },
                            {
                                type: "mrkdwn",
                                text: `*Customer:*\n${customerEmail || 'Unknown'}`
                            }
                        ]
                    }
                ]
            };

        default:
            return {
                ...baseMessage,
                text: `📋 Claim Update - ${claimData.claimId}`,
                blocks: [
                    {
                        type: "section",
                        text: {
                            type: "mrkdwn",
                            text: `📋 *Claim Update*\n\nClaim ${claimData.claimId} has been updated.`
                        }
                    }
                ]
            };
    }
}
