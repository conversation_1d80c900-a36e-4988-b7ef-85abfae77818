import { google } from 'googleapis';
import { handleInboundEmailWebhook } from '../email/inbound-handler';

export interface GmailConfig {
    clientId: string;
    clientSecret: string;
    refreshToken: string;
    supportEmail: string;
    pollIntervalMs: number;
}

export class GmailPoller {
    private gmail: any;
    private config: GmailConfig;
    private isPolling = false;
    private lastProcessedTime: Date;

    constructor(config: GmailConfig) {
        this.config = config;
        this.lastProcessedTime = new Date();
        this.setupGmailClient();
    }

    private setupGmailClient() {
        const oauth2Client = new google.auth.OAuth2(
            this.config.clientId,
            this.config.clientSecret,
            'urn:ietf:wg:oauth:2.0:oob'
        );

        oauth2Client.setCredentials({
            refresh_token: this.config.refreshToken
        });

        this.gmail = google.gmail({ version: 'v1', auth: oauth2Client });
    }

    async startPolling(): Promise<void> {
        if (this.isPolling) {
            console.log('📧 Gmail polling already running');
            return;
        }

        this.isPolling = true;
        console.log(`📧 Starting Gmail polling for ${this.config.supportEmail}`);
        console.log(`⏱️  Poll interval: ${this.config.pollIntervalMs}ms`);

        this.pollForNewEmails();
    }

    stopPolling(): void {
        this.isPolling = false;
        console.log('📧 Gmail polling stopped');
    }

    private async pollForNewEmails(): Promise<void> {
        while (this.isPolling) {
            try {
                await this.checkForNewEmails();
                await this.sleep(this.config.pollIntervalMs);
            } catch (error) {
                console.error('❌ Error polling Gmail:', error);
                await this.sleep(5000); // Wait 5 seconds on error
            }
        }
    }

    private async checkForNewEmails(): Promise<void> {
        try {
            // Search for new emails to support address
            const query = `to:${this.config.supportEmail} after:${this.formatDateForGmail(this.lastProcessedTime)}`;
            
            const response = await this.gmail.users.messages.list({
                userId: 'me',
                q: query,
                maxResults: 10
            });

            const messages = response.data.messages || [];
            
            if (messages.length > 0) {
                console.log(`📧 Found ${messages.length} new emails`);
                
                for (const message of messages) {
                    await this.processMessage(message.id);
                }
                
                this.lastProcessedTime = new Date();
            }
        } catch (error) {
            console.error('❌ Error checking for new emails:', error);
        }
    }

    private async processMessage(messageId: string): Promise<void> {
        try {
            const message = await this.gmail.users.messages.get({
                userId: 'me',
                id: messageId,
                format: 'full'
            });

            const email = this.parseGmailMessage(message.data);
            
            console.log('📨 Processing Gmail message:', {
                from: email.from,
                subject: email.subject,
                bodyLength: email.body.length
            });

            // Convert to webhook format and process
            const webhookPayload = {
                from: email.from,
                to: email.to,
                subject: email.subject,
                body: email.body,
                messageId: email.messageId,
                timestamp: email.timestamp,
                headers: email.headers
            };

            // Create mock request/response objects
            const mockReq = {
                body: webhookPayload,
                headers: { 'content-type': 'application/json' }
            } as any;

            const mockRes = {
                json: (data: any) => console.log('📤 Response:', data),
                status: (code: number) => ({
                    json: (data: any) => console.log(`📤 Response (${code}):`, data)
                })
            } as any;

            await handleInboundEmailWebhook(mockReq, mockRes);

        } catch (error) {
            console.error('❌ Error processing message:', error);
        }
    }

    private parseGmailMessage(message: any): any {
        const headers = message.payload.headers;
        const getHeader = (name: string) => 
            headers.find((h: any) => h.name.toLowerCase() === name.toLowerCase())?.value || '';

        let body = '';
        
        // Extract body from Gmail message
        if (message.payload.body?.data) {
            body = Buffer.from(message.payload.body.data, 'base64').toString();
        } else if (message.payload.parts) {
            // Multi-part message
            for (const part of message.payload.parts) {
                if (part.mimeType === 'text/plain' && part.body?.data) {
                    body = Buffer.from(part.body.data, 'base64').toString();
                    break;
                }
            }
        }

        return {
            from: getHeader('From'),
            to: getHeader('To'),
            subject: getHeader('Subject'),
            body: body,
            messageId: getHeader('Message-ID') || message.id,
            timestamp: new Date(parseInt(message.internalDate)),
            headers: headers.reduce((acc: any, h: any) => {
                acc[h.name] = h.value;
                return acc;
            }, {})
        };
    }

    private formatDateForGmail(date: Date): string {
        // Gmail search format: YYYY/MM/DD
        return date.toISOString().split('T')[0].replace(/-/g, '/');
    }

    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Setup function for easy configuration
export async function setupGmailPolling(): Promise<GmailPoller> {
    const config: GmailConfig = {
        clientId: process.env.GMAIL_CLIENT_ID || '',
        clientSecret: process.env.GMAIL_CLIENT_SECRET || '',
        refreshToken: process.env.GMAIL_REFRESH_TOKEN || '',
        supportEmail: process.env.SUPPORT_EMAIL_ADDRESS || '<EMAIL>',
        pollIntervalMs: parseInt(process.env.GMAIL_POLL_INTERVAL || '30000') // 30 seconds
    };

    // Validate configuration
    if (!config.clientId || !config.clientSecret || !config.refreshToken) {
        throw new Error('Gmail configuration missing. Set GMAIL_CLIENT_ID, GMAIL_CLIENT_SECRET, and GMAIL_REFRESH_TOKEN');
    }

    const poller = new GmailPoller(config);
    return poller;
}

// CLI command to start Gmail polling
if (require.main === module) {
    require('dotenv').config();
    
    setupGmailPolling()
        .then(poller => {
            console.log('🚀 Starting Gmail poller...');
            return poller.startPolling();
        })
        .catch(error => {
            console.error('❌ Failed to start Gmail poller:', error);
            process.exit(1);
        });
}
