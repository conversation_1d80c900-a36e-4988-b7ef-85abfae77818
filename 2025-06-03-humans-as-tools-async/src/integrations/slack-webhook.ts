import { Request, Response } from 'express';
import { getClaim } from '../claims/claims-processor';

/**
 * Handle Slack interactive component webhooks (button clicks)
 */
export async function handleSlackInteraction(req: Request, res: Response): Promise<void> {
    try {
        console.log('🔔 Received Slack interaction webhook');
        
        // Parse the payload
        const payload = JSON.parse(req.body.payload);
        console.log('📦 Interaction payload:', JSON.stringify(payload, null, 2));

        // Verify it's from Slack (in production, verify the signing secret)
        if (payload.type !== 'block_actions') {
            res.status(400).json({ error: 'Invalid interaction type' });
            return;
        }

        // Extract action details
        const action = payload.actions[0];
        const actionId = action.action_id;
        const value = action.value;
        const user = payload.user;

        console.log(`👤 User ${user.name} clicked ${actionId} with value: ${value}`);

        // Parse claim ID from value
        const claimId = value.split('_')[1];
        
        // Handle different actions
        let responseMessage = '';
        switch (actionId) {
            case 'approve_claim':
                responseMessage = await handleClaimApproval(claimId, user.name, true);
                break;
            case 'reject_claim':
                responseMessage = await handleClaimApproval(claimId, user.name, false);
                break;
            case 'request_info':
                responseMessage = await handleInfoRequest(claimId, user.name);
                break;
            default:
                responseMessage = '❓ Unknown action';
        }

        // Send response back to Slack
        res.json({
            response_type: 'in_channel',
            text: responseMessage,
            replace_original: false
        });

    } catch (error) {
        console.error('❌ Error handling Slack interaction:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
}

/**
 * Handle claim approval/rejection
 */
async function handleClaimApproval(claimId: string, approver: string, approved: boolean): Promise<string> {
    console.log(`${approved ? '✅' : '❌'} ${approver} ${approved ? 'approved' : 'rejected'} claim ${claimId}`);
    
    // In a real implementation, you would:
    // 1. Update the claim status in your database
    // 2. Send notification to customer
    // 3. Update internal systems
    
    const action = approved ? 'approved' : 'rejected';
    const emoji = approved ? '✅' : '❌';
    
    return `${emoji} *Claim ${claimId}* has been **${action}** by ${approver}`;
}

/**
 * Handle info request
 */
async function handleInfoRequest(claimId: string, requester: string): Promise<string> {
    console.log(`📝 ${requester} requested more info for claim ${claimId}`);
    
    // In a real implementation, you would:
    // 1. Send email to customer requesting more information
    // 2. Update claim status to "pending_info"
    // 3. Set up follow-up reminders
    
    return `📝 *${requester}* has requested additional information for claim **${claimId}**. Customer will be contacted.`;
}

/**
 * Verify Slack request signature (for production use)
 */
export function verifySlackSignature(req: Request): boolean {
    // In production, implement proper signature verification
    // using your Slack app's signing secret
    // See: https://api.slack.com/authentication/verifying-requests-from-slack
    return true;
}
