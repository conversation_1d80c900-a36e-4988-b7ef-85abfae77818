import { humanlayer } from 'humanlayer';
import { ClaimData } from '../claims/claims-processor';
import { EmailThreading } from '../email/threading';

export interface ApprovalRequest {
    claimData: ClaimData;
    approvalType: 'claim_approval' | 'high_value_claim' | 'complex_case';
    urgency: 'low' | 'normal' | 'high';
}

export interface ApprovalResult {
    approved: boolean;
    comment?: string;
    approver?: string;
    timestamp: Date;
}

/**
 * Request dual approval via email and Slack simultaneously
 */
export async function requestDualApproval(request: ApprovalRequest): Promise<ApprovalResult> {
    const { claimData, approvalType, urgency } = request;
    
    console.log(`🔄 Requesting dual approval for claim ${claimData.claimId}`);

    // Get configuration from environment
    const managerEmail = process.env.CLAIMS_MANAGER_EMAIL || process.env.HUMANLAYER_EMAIL_ADDRESS;
    const slackChannelId = process.env.CLAIMS_SLACK_CHANNEL || process.env.HUMANLAYER_SLACK_CHANNEL_ID;

    if (!managerEmail && !slackChannelId) {
        throw new Error('No approval channels configured. Set CLAIMS_MANAGER_EMAIL or CLAIMS_SLACK_CHANNEL');
    }

    // Create dual channel configuration
    const contactChannel = createDualApprovalChannel(claimData, managerEmail, slackChannelId);

    // Initialize HumanLayer
    const hl = humanlayer({
        apiKey: process.env.HUMANLAYER_API_KEY,
        runId: `claims-${claimData.claimId}`,
        contactChannel,
    });

    try {
        // Create approval request
        const approvalSpec = {
            fn: 'approve_claim',
            kwargs: {
                claim_id: claimData.claimId,
                customer_email: claimData.customerEmail,
                claim_type: claimData.claimType,
                policy_number: claimData.policyNumber,
                amount: claimData.amount,
                description: claimData.description,
                incident_date: claimData.incidentDate,
                urgency: urgency
            }
        };

        console.log('📤 Sending approval request to HumanLayer');
        console.log('🔧 Contact channel config:', JSON.stringify(contactChannel, null, 2));
        console.log('🔧 Approval spec:', JSON.stringify(approvalSpec, null, 2));

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('HumanLayer request timeout after 30 seconds')), 30000);
        });

        // Fetch approval (this will send to both email and Slack)
        const response = await Promise.race([
            hl.fetchHumanApproval({
                spec: approvalSpec
            }),
            timeoutPromise
        ]);

        console.log('📥 Received approval response:', response);

        return {
            approved: (response as any).approved || false,
            comment: (response as any).comment || '',
            approver: (response as any).approver || 'unknown',
            timestamp: new Date()
        };

    } catch (error) {
        console.error('❌ Error requesting approval:', error);
        console.error('❌ Error details:', error instanceof Error ? error.message : String(error));

        // Fallback to auto-rejection for safety
        return {
            approved: false,
            comment: `Approval system error: ${error instanceof Error ? error.message : 'Unknown error'} - please review manually`,
            timestamp: new Date()
        };
    }
}

/**
 * Create dual channel configuration for email + Slack
 */
function createDualApprovalChannel(
    claimData: ClaimData,
    managerEmail?: string,
    slackChannelId?: string
) {
    // For now, let's use just Slack to debug the issue
    if (slackChannelId) {
        console.log(`🔧 Creating Slack-only channel for: ${slackChannelId}`);
        return {
            slack: {
                channel_or_user_id: slackChannelId,
                context_about_channel_or_user: "claims approval team channel",
                experimental_slack_blocks: true
            }
        };
    }

    // Fallback to email if no Slack
    if (managerEmail) {
        console.log(`🔧 Creating email-only channel for: ${managerEmail}`);
        const emailChannel = EmailThreading.createManagerChannel(
            claimData.threadContext,
            managerEmail
        );

        // Enhance with custom template (note: template property may not be available in all HumanLayer versions)
        (emailChannel.email as any).template = createApprovalEmailTemplate(claimData);

        return emailChannel;
    }

    throw new Error('No contact channel configured');
}

/**
 * Create custom email template for claim approvals
 */
function createApprovalEmailTemplate(claimData: ClaimData): string {
    const urgencyColor = claimData.amount > 5000 ? '#ff4444' : '#ffa500';
    const urgencyText = claimData.amount > 5000 ? 'HIGH VALUE' : 'STANDARD';

    return `
<html>
<head>
    <title>Claim Approval Required</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <div style="background: ${urgencyColor}; color: white; padding: 15px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">🚨 CLAIM APPROVAL REQUIRED</h1>
        <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;">${urgencyText} CLAIM</p>
    </div>
    
    <div style="background: #f8f9fa; padding: 20px; border: 1px solid #ddd;">
        <h2 style="color: #333; margin-top: 0;">Claim Details</h2>
        
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #eee;">Claim ID:</td>
                <td style="padding: 8px; border-bottom: 1px solid #eee;">${claimData.claimId}</td>
            </tr>
            <tr>
                <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #eee;">Customer:</td>
                <td style="padding: 8px; border-bottom: 1px solid #eee;">${claimData.customerEmail}</td>
            </tr>
            <tr>
                <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #eee;">Type:</td>
                <td style="padding: 8px; border-bottom: 1px solid #eee;">${claimData.claimType}</td>
            </tr>
            <tr>
                <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #eee;">Amount:</td>
                <td style="padding: 8px; border-bottom: 1px solid #eee; font-size: 18px; font-weight: bold; color: #d63384;">$${claimData.amount}</td>
            </tr>
            ${claimData.policyNumber ? `
            <tr>
                <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #eee;">Policy:</td>
                <td style="padding: 8px; border-bottom: 1px solid #eee;">${claimData.policyNumber}</td>
            </tr>
            ` : ''}
            ${claimData.incidentDate ? `
            <tr>
                <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #eee;">Incident Date:</td>
                <td style="padding: 8px; border-bottom: 1px solid #eee;">${claimData.incidentDate}</td>
            </tr>
            ` : ''}
        </table>
        
        <div style="margin: 20px 0; padding: 15px; background: white; border-radius: 5px; border-left: 4px solid #007bff;">
            <h3 style="margin-top: 0; color: #333;">Description:</h3>
            <p style="margin-bottom: 0; line-height: 1.5;">${claimData.description}</p>
        </div>
    </div>
    
    <div style="background: white; padding: 20px; text-align: center; border: 1px solid #ddd; border-top: none; border-radius: 0 0 8px 8px;">
        <p style="margin-bottom: 20px; color: #666;">Please review this claim and make your decision:</p>
        
        <a href="{{ urls.base_url }}?approve=true" 
           style="display: inline-block; background: #28a745; color: white; padding: 12px 30px; 
                  text-decoration: none; border-radius: 5px; margin: 0 10px; font-weight: bold;">
            ✅ APPROVE CLAIM
        </a>
        
        <a href="{{ urls.base_url }}?reject=true" 
           style="display: inline-block; background: #dc3545; color: white; padding: 12px 30px; 
                  text-decoration: none; border-radius: 5px; margin: 0 10px; font-weight: bold;">
            ❌ REJECT CLAIM
        </a>
    </div>
    
    <div style="margin-top: 15px; padding: 10px; background: #f1f3f4; border-radius: 5px; font-size: 12px; color: #666;">
        <p style="margin: 0;"><strong>Thread Info:</strong> This email is part of the conversation with ${claimData.customerEmail}</p>
        <p style="margin: 5px 0 0 0;">Original Subject: ${claimData.threadContext.originalSubject}</p>
    </div>
</body>
</html>`;
}

/**
 * Send notification to team about claim status
 */
export async function notifyTeam(claimData: ClaimData, status: string, message: string): Promise<void> {
    const teamSlackChannel = process.env.CLAIMS_TEAM_SLACK_CHANNEL;
    
    if (!teamSlackChannel) {
        console.log('📢 Team notification (no Slack configured):', { claimData: claimData.claimId, status, message });
        return;
    }

    try {
        const hl = humanlayer({
            apiKey: process.env.HUMANLAYER_API_KEY,
            runId: `team-notification-${claimData.claimId}`,
            contactChannel: {
                slack: {
                    channel_or_user_id: teamSlackChannel,
                    context_about_channel_or_user: "claims processing team"
                }
            }
        });

        await hl.createHumanContact({
            spec: {
                msg: `📋 Claim Update: ${claimData.claimId}\nStatus: ${status}\n${message}`,
                state: {
                    claim_id: claimData.claimId
                }
            }
        });

        console.log('📢 Team notified via Slack');
    } catch (error) {
        console.error('❌ Failed to notify team:', error);
    }
}
