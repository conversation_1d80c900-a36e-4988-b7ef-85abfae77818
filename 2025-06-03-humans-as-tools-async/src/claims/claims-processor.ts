import { b, ProcessClaim, RequestClaimInfo } from '../../baml_client';
import { InboundEmail, EmailThreadContext } from '../email/inbound-handler';
import { EmailThreading } from '../email/threading';
import { requestDualApproval } from '../integrations/humanlayer';
// Note: sendCustomerResponse will be imported dynamically to avoid circular dependency

export interface ClaimData {
    claimId: string;
    customerEmail: string;
    policyNumber?: string;
    claimType: string;
    incidentDate?: string;
    amount: number;
    description: string;
    requiresApproval: boolean;
    status: 'pending' | 'approved' | 'rejected' | 'processing' | 'completed';
    threadContext: EmailThreadContext;
}

/**
 * Main entry point for processing claims emails
 */
export async function processClaimsEmail(
    email: InboundEmail, 
    threadContext: EmailThreadContext
): Promise<void> {
    try {
        console.log('🏥 Starting claims processing for:', email.from);

        // Analyze the email content using BAML
        const analysis = await b.AnalyzeClaim(email.body, email.from);
        
        console.log('🔍 Claim analysis result:', analysis);

        if (analysis.intent === 'request_claim_info') {
            // Missing information - ask customer for more details
            await handleMissingInformation(analysis, threadContext);
        } else if (analysis.intent === 'process_claim') {
            // Valid claim - process it
            await handleValidClaim(analysis, threadContext);
        }

    } catch (error) {
        console.error('❌ Error processing claims email:', error);
        
        // Send error response to customer
        const { sendCustomerResponse } = await import('../email/outbound');
        await sendCustomerResponse(
            threadContext,
            "We're sorry, but there was an error processing your claim submission. " +
            "Please try again or contact our support team directly.",
            'error'
        );
    }
}

/**
 * Handle case where claim information is incomplete
 */
async function handleMissingInformation(
    analysis: RequestClaimInfo,
    threadContext: EmailThreadContext
): Promise<void> {
    console.log('📝 Requesting additional claim information');

    const missingFields = analysis.missing_fields.join(', ');
    const message = `
Thank you for contacting us about your claim. To process your request, we need some additional information:

Missing information: ${missingFields}

${analysis.message}

Please reply to this email with the requested details, and we'll process your claim promptly.

Best regards,
Claims Processing Team
    `.trim();

    const { sendCustomerResponse } = await import('../email/outbound');
    await sendCustomerResponse(threadContext, message, 'info_request');
}

/**
 * Handle valid claim submission
 */
async function handleValidClaim(
    analysis: ProcessClaim,
    threadContext: EmailThreadContext
): Promise<void> {
    // Generate unique claim ID
    const claimId = generateClaimId();
    
    console.log(`📋 Processing valid claim: ${claimId}`);

    // Create claim data object
    const claimData: ClaimData = {
        claimId,
        customerEmail: threadContext.customerEmail,
        policyNumber: analysis.policy_number,
        claimType: analysis.claim_type,
        incidentDate: analysis.incident_date,
        amount: analysis.amount,
        description: analysis.description,
        requiresApproval: analysis.requires_approval,
        status: 'pending',
        threadContext
    };

    // Store claim data (in production, save to database)
    await storeClaim(claimData);

    if (claimData.requiresApproval) {
        console.log('⚠️ Claim requires approval, routing to manager');
        await handleApprovalRequired(claimData);
    } else {
        console.log('✅ Auto-approving claim');
        await handleAutoApproval(claimData);
    }
}

/**
 * Handle claims that require manager approval
 */
async function handleApprovalRequired(claimData: ClaimData): Promise<void> {
    // Send acknowledgment to customer
    const { sendCustomerResponse } = await import('../email/outbound');
    await sendCustomerResponse(
        claimData.threadContext,
        `Thank you for submitting your ${claimData.claimType} claim.

Claim ID: ${claimData.claimId}
Estimated Amount: $${claimData.amount}

Your claim is being reviewed and requires manager approval. You will receive an update within 24 hours.

Best regards,
Claims Processing Team`,
        'acknowledgment'
    );

    // Request approval from manager via email and Slack
    const approvalResult = await requestDualApproval({
        claimData,
        approvalType: 'claim_approval',
        urgency: claimData.amount > 5000 ? 'high' : 'normal'
    });

    if (approvalResult.approved) {
        await handleClaimApproved(claimData, approvalResult.comment);
    } else {
        await handleClaimRejected(claimData, approvalResult.comment);
    }
}

/**
 * Handle auto-approved claims (under threshold)
 */
async function handleAutoApproval(claimData: ClaimData): Promise<void> {
    claimData.status = 'approved';
    await storeClaim(claimData);

    const { sendCustomerResponse: sendResponse1 } = await import('../email/outbound');
    await sendResponse1(
        claimData.threadContext,
        `Great news! Your ${claimData.claimType} claim has been approved.

Claim ID: ${claimData.claimId}
Approved Amount: $${claimData.amount}
Processing Time: 3-5 business days

You will receive payment via your preferred method on file. If you have any questions, please reference your claim ID.

Best regards,
Claims Processing Team`,
        'approval'
    );

    console.log(`✅ Auto-approved claim ${claimData.claimId} for $${claimData.amount}`);
}

/**
 * Handle approved claims after manager review
 */
async function handleClaimApproved(claimData: ClaimData, approvalComment?: string): Promise<void> {
    claimData.status = 'approved';
    await storeClaim(claimData);

    const message = `Excellent news! Your ${claimData.claimType} claim has been approved by our claims manager.

Claim ID: ${claimData.claimId}
Approved Amount: $${claimData.amount}
${approvalComment ? `Manager Notes: ${approvalComment}` : ''}

Processing will begin immediately, and you can expect payment within 3-5 business days.

Best regards,
Claims Processing Team`;

    const { sendCustomerResponse: sendResponse2 } = await import('../email/outbound');
    await sendResponse2(claimData.threadContext, message, 'approval');
    
    console.log(`✅ Manager approved claim ${claimData.claimId}`);
}

/**
 * Handle rejected claims
 */
async function handleClaimRejected(claimData: ClaimData, rejectionReason?: string): Promise<void> {
    claimData.status = 'rejected';
    await storeClaim(claimData);

    const message = `We have reviewed your ${claimData.claimType} claim and unfortunately cannot approve it at this time.

Claim ID: ${claimData.claimId}
${rejectionReason ? `Reason: ${rejectionReason}` : ''}

If you believe this decision is incorrect or have additional information to provide, please reply to this email with supporting documentation.

Best regards,
Claims Processing Team`;

    const { sendCustomerResponse: sendResponse3 } = await import('../email/outbound');
    await sendResponse3(claimData.threadContext, message, 'rejection');
    
    console.log(`❌ Claim ${claimData.claimId} rejected: ${rejectionReason}`);
}

/**
 * Generate unique claim ID
 */
function generateClaimId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `CLM-${timestamp}-${random}`;
}

/**
 * Store claim data (mock implementation - replace with database)
 */
async function storeClaim(claimData: ClaimData): Promise<void> {
    // In production, save to database
    console.log('💾 Storing claim data:', {
        claimId: claimData.claimId,
        status: claimData.status,
        amount: claimData.amount,
        customer: claimData.customerEmail
    });
    
    // Mock storage - in production use your database
    // await database.claims.upsert(claimData);
}

/**
 * Retrieve claim data (mock implementation)
 */
export async function getClaim(claimId: string): Promise<ClaimData | null> {
    // In production, fetch from database
    console.log('🔍 Retrieving claim:', claimId);
    return null; // Mock - replace with database query
}
