import { EmailThreadContext } from '../email/inbound-handler';

export interface ClaimData {
    claimId: string;
    customerEmail: string;
    policyNumber?: string;
    claimType: string;
    incidentDate?: string;
    amount: number;
    description: string;
    requiresApproval: boolean;
    status: 'pending' | 'approved' | 'rejected' | 'processing' | 'completed';
    threadContext: EmailThreadContext;
}

export interface ApprovalRequest {
    claimData: ClaimData;
    approvalType: 'claim_approval' | 'payment_approval' | 'exception_approval';
    urgency: 'low' | 'normal' | 'high' | 'urgent';
    requestedBy?: string;
    deadline?: Date;
}

export interface ApprovalResult {
    approved: boolean;
    comment?: string;
    approver?: string;
    timestamp: Date;
}

export interface ClaimProcessingOptions {
    autoApprovalThreshold: number;
    requiresManagerApproval: boolean;
    notificationChannels: {
        email?: boolean;
        slack?: boolean;
        sms?: boolean;
    };
}

export type ClaimStatus = 'pending' | 'approved' | 'rejected' | 'processing' | 'completed';
export type ClaimType = 'auto' | 'home' | 'health' | 'life' | 'travel' | 'business';
export type UrgencyLevel = 'low' | 'normal' | 'high' | 'urgent';
