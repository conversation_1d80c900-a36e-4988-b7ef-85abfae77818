import { humanlayer } from 'humanlayer';
import { EmailThreadContext } from './inbound-handler';
import { EmailThreading } from './threading';

export type ResponseType = 'acknowledgment' | 'info_request' | 'approval' | 'rejection' | 'error' | 'update';

export interface CustomerResponse {
    threadContext: EmailThreadContext;
    message: string;
    responseType: ResponseType;
    claimId?: string;
}

/**
 * Send response back to customer in the same email thread
 */
export async function sendCustomerResponse(
    threadContext: EmailThreadContext,
    message: string,
    responseType: ResponseType,
    claimId?: string
): Promise<void> {
    try {
        console.log(`📤 Sending ${responseType} response to customer:`, threadContext.customerEmail);

        // Validate thread context
        if (!EmailThreading.validateContext(threadContext)) {
            throw new Error('Invalid thread context for customer response');
        }

        // Create threaded email channel
        const customerChannel = EmailThreading.createCustomerChannel(threadContext);

        // Enhance with custom template (note: template property may not be available in all HumanLayer versions)
        (customerChannel.email as any).template = createCustomerEmailTemplate(
            threadContext,
            message,
            responseType,
            claimId
        );

        // Initialize HumanLayer for sending response
        const hl = humanlayer({
            apiKey: process.env.HUMANLAYER_API_KEY,
            runId: `customer-response-${claimId || Date.now()}`,
            contactChannel: customerChannel
        });

        // Send the response using HumanLayer's human contact feature
        await hl.createHumanContact({
            spec: {
                msg: message,
                state: {
                    thread_id: threadContext.originalMessageId,
                    claim_id: claimId,
                    response_type: responseType
                }
            }
        });

        console.log('✅ Customer response sent successfully');

    } catch (error) {
        console.error('❌ Failed to send customer response:', error);
        throw error;
    }
}

/**
 * Create custom email template for customer responses
 */
function createCustomerEmailTemplate(
    threadContext: EmailThreadContext,
    message: string,
    responseType: ResponseType,
    claimId?: string
): string {
    const { headerColor, headerIcon, headerText } = getResponseTypeStyles(responseType);
    
    return `
<html>
<head>
    <title>${headerText}</title>
    <meta charset="utf-8">
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #f5f5f5;">
    <!-- Header -->
    <div style="background: ${headerColor}; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">${headerIcon} ${headerText}</h1>
        ${claimId ? `<p style="margin: 5px 0 0 0; opacity: 0.9;">Claim ID: ${claimId}</p>` : ''}
    </div>
    
    <!-- Main Content -->
    <div style="background: white; padding: 30px; border: 1px solid #ddd;">
        <div style="line-height: 1.6; color: #333; font-size: 16px;">
            ${formatMessage(message)}
        </div>
        
        ${responseType === 'info_request' ? `
        <div style="margin-top: 25px; padding: 15px; background: #e3f2fd; border-left: 4px solid #2196f3; border-radius: 4px;">
            <p style="margin: 0; font-weight: bold; color: #1976d2;">💡 How to Reply:</p>
            <p style="margin: 5px 0 0 0; color: #1976d2;">Simply reply to this email with the requested information. Our system will automatically process your response.</p>
        </div>
        ` : ''}
        
        ${responseType === 'approval' ? `
        <div style="margin-top: 25px; padding: 15px; background: #e8f5e8; border-left: 4px solid #4caf50; border-radius: 4px;">
            <p style="margin: 0; font-weight: bold; color: #2e7d32;">🎉 What's Next:</p>
            <p style="margin: 5px 0 0 0; color: #2e7d32;">Your claim is now being processed. You'll receive payment within 3-5 business days via your preferred method on file.</p>
        </div>
        ` : ''}
        
        ${responseType === 'rejection' ? `
        <div style="margin-top: 25px; padding: 15px; background: #ffebee; border-left: 4px solid #f44336; border-radius: 4px;">
            <p style="margin: 0; font-weight: bold; color: #c62828;">📞 Need Help?</p>
            <p style="margin: 5px 0 0 0; color: #c62828;">If you have questions about this decision or additional information to provide, please reply to this email or call our claims department.</p>
        </div>
        ` : ''}
    </div>
    
    <!-- Footer -->
    <div style="background: #f8f9fa; padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 8px 8px;">
        <div style="text-align: center; color: #666; font-size: 14px;">
            <p style="margin: 0 0 10px 0;">
                <strong>Claims Processing Team</strong><br>
                ${threadContext.supportEmail}
            </p>
            
            ${claimId ? `
            <p style="margin: 10px 0; padding: 10px; background: white; border-radius: 4px; font-family: monospace;">
                Reference: ${claimId}
            </p>
            ` : ''}
            
            <p style="margin: 10px 0 0 0; font-size: 12px; color: #999;">
                This email is part of your ongoing conversation. Reply to continue the thread.
            </p>
        </div>
    </div>
    
    <!-- Thread Information (hidden but important for email clients) -->
    <div style="display: none;">
        Thread-Topic: ${threadContext.originalSubject}
        In-Reply-To: ${threadContext.originalMessageId}
        References: ${threadContext.originalMessageId}
    </div>
</body>
</html>`;
}

/**
 * Get styling based on response type
 */
function getResponseTypeStyles(responseType: ResponseType) {
    switch (responseType) {
        case 'acknowledgment':
            return {
                headerColor: '#2196f3',
                headerIcon: '📋',
                headerText: 'Claim Received'
            };
        case 'info_request':
            return {
                headerColor: '#ff9800',
                headerIcon: '📝',
                headerText: 'Additional Information Needed'
            };
        case 'approval':
            return {
                headerColor: '#4caf50',
                headerIcon: '✅',
                headerText: 'Claim Approved'
            };
        case 'rejection':
            return {
                headerColor: '#f44336',
                headerIcon: '❌',
                headerText: 'Claim Decision'
            };
        case 'error':
            return {
                headerColor: '#9c27b0',
                headerIcon: '⚠️',
                headerText: 'Processing Error'
            };
        case 'update':
            return {
                headerColor: '#607d8b',
                headerIcon: '📢',
                headerText: 'Claim Update'
            };
        default:
            return {
                headerColor: '#666',
                headerIcon: '📧',
                headerText: 'Claim Communication'
            };
    }
}

/**
 * Format message content with proper line breaks and styling
 */
function formatMessage(message: string): string {
    return message
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .map(line => `<p style="margin: 0 0 15px 0;">${escapeHtml(line)}</p>`)
        .join('');
}

/**
 * Escape HTML characters in message content
 */
function escapeHtml(text: string): string {
    const div = { innerHTML: '' } as any;
    div.textContent = text;
    return div.innerHTML || text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
}

/**
 * Send bulk notifications to multiple customers
 */
export async function sendBulkCustomerNotifications(
    responses: CustomerResponse[]
): Promise<void> {
    console.log(`📤 Sending ${responses.length} bulk customer notifications`);
    
    const promises = responses.map(response => 
        sendCustomerResponse(
            response.threadContext,
            response.message,
            response.responseType,
            response.claimId
        )
    );
    
    try {
        await Promise.all(promises);
        console.log('✅ All bulk notifications sent successfully');
    } catch (error) {
        console.error('❌ Some bulk notifications failed:', error);
        throw error;
    }
}

/**
 * Send follow-up reminder to customer
 */
export async function sendFollowUpReminder(
    threadContext: EmailThreadContext,
    claimId: string,
    daysSinceLastContact: number
): Promise<void> {
    const message = `
We wanted to follow up on your claim submission from ${daysSinceLastContact} days ago.

If you need any assistance or have questions about your claim status, please don't hesitate to reach out.

We're here to help make the claims process as smooth as possible for you.
    `.trim();

    await sendCustomerResponse(threadContext, message, 'update', claimId);
}
