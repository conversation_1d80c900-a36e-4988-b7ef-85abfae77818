import express, { Request, Response } from 'express';
import { processClaimsEmail } from '../claims/claims-processor';

export interface InboundEmail {
    from: string;
    to: string;
    subject: string;
    body: string;
    messageId: string;
    timestamp: Date;
    headers: Record<string, string>;
}

export interface EmailThreadContext {
    customerEmail: string;
    originalMessageId: string;
    originalSubject: string;
    supportEmail: string;
}

/**
 * Parse incoming email from webhook payload
 * Supports SendGrid, Mailgun, and generic webhook formats
 */
export function parseInboundEmail(payload: any): InboundEmail {
    console.log('🔍 Parsing email payload:', {
        hasFrom: !!payload.from,
        hasTo: !!payload.to,
        hasSubject: !!payload.subject,
        hasBody: !!payload.body,
        hasText: !!payload.text,
        hasHtml: !!payload.html,
        bodyContent: payload.body?.substring(0, 100) + '...'
    });

    // SendGrid Inbound Parse format
    if (payload.from && payload.to && payload.subject) {
        const result = {
            from: payload.from,
            to: payload.to,
            subject: payload.subject,
            body: payload.text || payload.html || payload.body || '',
            messageId: payload.headers?.['Message-ID'] || generateMessageId(),
            timestamp: new Date(),
            headers: payload.headers || {}
        };
        console.log('📧 Using SendGrid format, body length:', result.body.length);
        return result;
    }

    // Mailgun format
    if (payload.sender && payload.recipient) {
        return {
            from: payload.sender,
            to: payload.recipient,
            subject: payload.Subject || '',
            body: payload['body-plain'] || payload['body-html'] || payload.body || '',
            messageId: payload['Message-Id'] || generateMessageId(),
            timestamp: new Date(payload.timestamp * 1000),
            headers: {}
        };
    }

    // Generic format
    return {
        from: payload.from || '',
        to: payload.to || '',
        subject: payload.subject || '',
        body: payload.body || payload.content || payload.text || payload.html || '',
        messageId: payload.messageId || generateMessageId(),
        timestamp: new Date(),
        headers: payload.headers || {}
    };
}

/**
 * Create email threading context for maintaining conversation
 */
export function createThreadContext(email: InboundEmail): EmailThreadContext {
    return {
        customerEmail: email.from,
        originalMessageId: email.messageId,
        originalSubject: email.subject,
        supportEmail: email.to
    };
}

/**
 * Check if email is a claims-related request
 */
export function isClaimsEmail(email: InboundEmail): boolean {
    const claimsKeywords = [
        'claim', 'accident', 'damage', 'incident', 'policy',
        'insurance', 'refund', 'compensation', 'liability',
        'collision', 'theft', 'fire', 'flood', 'medical'
    ];
    
    const content = `${email.subject} ${email.body}`.toLowerCase();
    return claimsKeywords.some(keyword => content.includes(keyword));
}

/**
 * Express middleware to handle inbound email webhooks
 */
export async function handleInboundEmailWebhook(req: Request, res: Response) {
    try {
        console.log('📧 Received inbound email webhook:', {
            headers: req.headers,
            body: typeof req.body === 'string' ? req.body.substring(0, 200) : req.body
        });

        // Parse the incoming email
        const email = parseInboundEmail(req.body);
        
        console.log('📨 Parsed email:', {
            from: email.from,
            to: email.to,
            subject: email.subject,
            bodyLength: email.body.length
        });

        // Create threading context
        const threadContext = createThreadContext(email);

        // Check if this is a claims email
        if (isClaimsEmail(email)) {
            console.log('🏥 Processing as claims email');
            
            // Start claims processing workflow
            await processClaimsEmail(email, threadContext);
            
            res.json({ 
                status: 'success', 
                message: 'Claims email processed',
                claimId: `CLM-${Date.now()}`
            });
        } else {
            console.log('📝 Non-claims email, forwarding to general support');
            
            // Handle as general support email
            res.json({ 
                status: 'success', 
                message: 'Email forwarded to support team' 
            });
        }

    } catch (error) {
        console.error('❌ Error processing inbound email:', error);
        res.status(500).json({ 
            status: 'error', 
            message: 'Failed to process email',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
}

/**
 * Generate a unique message ID for emails without one
 */
function generateMessageId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return `<${timestamp}.${random}@claims-processor>`;
}

/**
 * Setup email webhook routes
 */
export function setupEmailWebhookRoutes(app: express.Application) {
    // SendGrid webhook endpoint
    app.post('/webhook/email/sendgrid', handleInboundEmailWebhook);
    
    // Mailgun webhook endpoint  
    app.post('/webhook/email/mailgun', handleInboundEmailWebhook);
    
    // Generic webhook endpoint
    app.post('/webhook/email/inbound', handleInboundEmailWebhook);
    
    // Test endpoint for development
    app.post('/webhook/email/test', async (req, res) => {
        const testEmail: InboundEmail = {
            from: req.body.from || '<EMAIL>',
            to: '<EMAIL>',
            subject: req.body.subject || 'Test claim submission',
            body: req.body.body || 'This is a test claim email',
            messageId: generateMessageId(),
            timestamp: new Date(),
            headers: {}
        };
        
        await handleInboundEmailWebhook(
            { body: testEmail } as Request,
            res as Response
        );
    });
    
    console.log('📧 Email webhook routes configured:');
    console.log('   POST /webhook/email/sendgrid');
    console.log('   POST /webhook/email/mailgun');
    console.log('   POST /webhook/email/inbound');
    console.log('   POST /webhook/email/test');
}
