import { EmailThreadContext } from './inbound-handler';

export interface EmailThreadingOptions {
    customerEmail: string;
    originalMessageId: string;
    originalSubject: string;
    supportEmail: string;
}

export interface ThreadedEmailChannel {
    email: {
        address: string;
        in_reply_to_message_id?: string;
        references_message_id?: string;
        experimental_subject_line?: string;
        context_about_user?: string;
    };
}

/**
 * Create email channel that maintains thread with customer
 */
export function createCustomerThreadChannel(threadContext: EmailThreadContext): ThreadedEmailChannel {
    return {
        email: {
            address: threadContext.customerEmail,
            in_reply_to_message_id: threadContext.originalMessageId,
            references_message_id: threadContext.originalMessageId,
            experimental_subject_line: `Re: ${threadContext.originalSubject}`,
            context_about_user: "the customer who submitted the claim"
        }
    };
}

/**
 * Create email channel for manager approval that includes customer in thread
 */
export function createManagerApprovalChannel(
    threadContext: EmailThreadContext,
    managerEmail: string
): ThreadedEmailChannel {
    return {
        email: {
            address: managerEmail,
            in_reply_to_message_id: threadContext.originalMessageId,
            references_message_id: threadContext.originalMessageId,
            experimental_subject_line: `APPROVAL NEEDED: ${threadContext.originalSubject}`,
            context_about_user: "the claims manager who needs to approve this claim"
        }
    };
}

/**
 * Create composite channel for dual approval (email + Slack)
 */
export function createDualApprovalChannel(
    threadContext: EmailThreadContext,
    managerEmail: string,
    slackChannelId: string
) {
    return {
        composite: {
            channels: [
                // Email to manager (threaded)
                createManagerApprovalChannel(threadContext, managerEmail),
                // Slack notification
                {
                    slack: {
                        channel_or_user_id: slackChannelId,
                        context_about_channel_or_user: "claims approval team channel"
                    }
                }
            ]
        }
    };
}

/**
 * Create email channel for internal team notifications
 */
export function createTeamNotificationChannel(
    threadContext: EmailThreadContext,
    teamEmail: string
): ThreadedEmailChannel {
    return {
        email: {
            address: teamEmail,
            experimental_subject_line: `CLAIM PROCESSED: ${threadContext.originalSubject}`,
            context_about_user: "the claims processing team"
        }
    };
}

/**
 * Extract email thread information from headers
 */
export function extractThreadInfo(headers: Record<string, string>) {
    return {
        messageId: headers['Message-ID'] || headers['message-id'],
        inReplyTo: headers['In-Reply-To'] || headers['in-reply-to'],
        references: headers['References'] || headers['references'],
        subject: headers['Subject'] || headers['subject']
    };
}

/**
 * Generate proper email headers for threading
 */
export function generateThreadHeaders(threadContext: EmailThreadContext) {
    return {
        'In-Reply-To': threadContext.originalMessageId,
        'References': threadContext.originalMessageId,
        'Thread-Topic': threadContext.originalSubject,
        'Thread-Index': generateThreadIndex(threadContext.originalMessageId)
    };
}

/**
 * Generate thread index for Outlook compatibility
 */
function generateThreadIndex(messageId: string): string {
    // Simple thread index generation
    // In production, you'd want a more sophisticated algorithm
    const hash = messageId.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
    }, 0);
    
    return Math.abs(hash).toString(16).padStart(8, '0');
}

/**
 * Validate email threading context
 */
export function validateThreadContext(threadContext: EmailThreadContext): boolean {
    return !!(
        threadContext.customerEmail &&
        threadContext.originalMessageId &&
        threadContext.originalSubject &&
        threadContext.supportEmail
    );
}

/**
 * Create email template with proper threading
 */
export function createThreadedEmailTemplate(
    threadContext: EmailThreadContext,
    content: string,
    isApprovalRequest: boolean = false
): string {
    const headers = generateThreadHeaders(threadContext);
    
    let template = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>${isApprovalRequest ? 'Approval Required' : 'Claim Update'}</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #333; margin: 0;">
            ${isApprovalRequest ? '🚨 Approval Required' : '📋 Claim Update'}
        </h2>
        <p style="color: #666; margin: 5px 0 0 0;">
            Customer: ${threadContext.customerEmail}
        </p>
    </div>
    
    <div style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
        ${content}
    </div>
    
    <div style="margin-top: 20px; padding: 15px; background: #f1f3f4; border-radius: 8px; font-size: 12px; color: #666;">
        <p><strong>Thread Information:</strong></p>
        <p>Original Subject: ${threadContext.originalSubject}</p>
        <p>Message ID: ${threadContext.originalMessageId}</p>
        <p>Customer: ${threadContext.customerEmail}</p>
    </div>
</body>
</html>`;

    return template;
}

/**
 * Email threading utilities
 */
export const EmailThreading = {
    createCustomerChannel: createCustomerThreadChannel,
    createManagerChannel: createManagerApprovalChannel,
    createDualChannel: createDualApprovalChannel,
    createTeamChannel: createTeamNotificationChannel,
    generateHeaders: generateThreadHeaders,
    validateContext: validateThreadContext,
    createTemplate: createThreadedEmailTemplate
};
