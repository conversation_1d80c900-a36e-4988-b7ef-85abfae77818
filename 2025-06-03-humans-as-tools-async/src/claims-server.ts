import express, { Request, Response } from 'express';
import { setupEmailWebhookRoutes } from './email/inbound-handler';
import { getClaim } from './claims/claims-processor';
import { sendCustomerResponse } from './email/outbound';

const app = express();

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.text({ type: 'text/plain', limit: '10mb' }));

// CORS for development
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'claims-processing-server',
        version: '1.0.0'
    });
});

// Claims API endpoints
app.get('/api/claims/:claimId', async (req: Request, res: Response): Promise<void> => {
    try {
        const { claimId } = req.params;
        const claim = await getClaim(claimId);
        
        if (!claim) {
            res.status(404).json({
                error: 'Claim not found',
                claimId
            });
            return;
        }

        res.json(claim);
    } catch (error) {
        console.error('Error fetching claim:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

// Test endpoint for manual claim submission
app.post('/api/claims/test', async (req: Request, res: Response): Promise<void> => {
    try {
        const {
            customerEmail = '<EMAIL>',
            subject = 'Test claim submission',
            body = 'This is a test claim for auto accident. Policy #12345, $2000 damage.'
        } = req.body;

        // Simulate webhook call
        const webhookPayload = {
            from: customerEmail,
            to: '<EMAIL>',
            subject: subject,
            body: body,
            messageId: `<test-${Date.now()}@claims-processor>`,
            timestamp: new Date()
        };

        // Forward to webhook handler
        const webhookReq = {
            body: webhookPayload,
            headers: { 'content-type': 'application/json' }
        } as Request;

        const webhookRes = {
            json: (data: any) => res.json(data),
            status: (code: number) => ({ json: (data: any) => res.status(code).json(data) })
        } as any;

        const { handleInboundEmailWebhook } = await import('./email/inbound-handler');
        await handleInboundEmailWebhook(webhookReq, webhookRes);

    } catch (error) {
        console.error('Error in test endpoint:', error);
        res.status(500).json({
            error: 'Test failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

// Setup email webhook routes
setupEmailWebhookRoutes(app);

// Error handling middleware
app.use((error: Error, req: Request, res: Response, next: any) => {
    console.error('Unhandled error:', error);
    res.status(500).json({
        error: 'Internal server error',
        message: error.message,
        timestamp: new Date().toISOString()
    });
});

// 404 handler
app.use((req: Request, res: Response) => {
    res.status(404).json({
        error: 'Not found',
        path: req.path,
        method: req.method,
        timestamp: new Date().toISOString()
    });
});

/**
 * Start the claims processing server
 */
export function startClaimsServer(port: number = 3001): void {
    const server = app.listen(port, () => {
        console.log('🏥 Claims Processing Server Started');
        console.log(`📍 Server running on port ${port}`);
        console.log('');
        console.log('📧 Email Webhook Endpoints:');
        console.log(`   POST http://localhost:${port}/webhook/email/sendgrid`);
        console.log(`   POST http://localhost:${port}/webhook/email/mailgun`);
        console.log(`   POST http://localhost:${port}/webhook/email/inbound`);
        console.log(`   POST http://localhost:${port}/webhook/email/test`);
        console.log('');
        console.log('🔧 API Endpoints:');
        console.log(`   GET  http://localhost:${port}/health`);
        console.log(`   GET  http://localhost:${port}/api/claims/:claimId`);
        console.log(`   POST http://localhost:${port}/api/claims/test`);
        console.log('');
        console.log('🧪 Test the system:');
        console.log(`   curl -X POST http://localhost:${port}/api/claims/test \\`);
        console.log(`     -H "Content-Type: application/json" \\`);
        console.log(`     -d '{"customerEmail":"<EMAIL>","subject":"Car accident claim","body":"I had an accident yesterday, policy #12345, $3000 damage"}'`);
        console.log('');
    });

    server.on('error', (error: Error) => {
        console.error('❌ Server error:', error);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
        console.log('📴 Received SIGTERM, shutting down gracefully');
        server.close(() => {
            console.log('✅ Server closed');
            process.exit(0);
        });
    });

    process.on('SIGINT', () => {
        console.log('📴 Received SIGINT, shutting down gracefully');
        server.close(() => {
            console.log('✅ Server closed');
            process.exit(0);
        });
    });
}

// Start server if this file is run directly
if (require.main === module) {
    // Load environment variables
    require('dotenv').config();
    
    // Validate required environment variables
    const requiredEnvVars = [
        'OPENAI_API_KEY',
        'HUMANLAYER_API_KEY'
    ];
    
    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingEnvVars.length > 0) {
        console.error('❌ Missing required environment variables:');
        missingEnvVars.forEach(varName => console.error(`   - ${varName}`));
        console.error('');
        console.error('Please add these to your .env file');
        process.exit(1);
    }
    
    // Check for approval channels
    const hasEmailApproval = !!process.env.CLAIMS_MANAGER_EMAIL || !!process.env.HUMANLAYER_EMAIL_ADDRESS;
    const hasSlackApproval = !!process.env.CLAIMS_SLACK_CHANNEL || !!process.env.HUMANLAYER_SLACK_CHANNEL_ID;
    
    if (!hasEmailApproval && !hasSlackApproval) {
        console.warn('⚠️  No approval channels configured!');
        console.warn('Add one of these to your .env file:');
        console.warn('   - CLAIMS_MANAGER_EMAIL=<EMAIL>');
        console.warn('   - CLAIMS_SLACK_CHANNEL=C1234567890');
        console.warn('');
    }
    
    const port = parseInt(process.env.CLAIMS_SERVER_PORT || '3001');
    startClaimsServer(port);
}
