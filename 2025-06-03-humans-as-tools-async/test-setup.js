#!/usr/bin/env node

// Simple test to verify the setup is working
console.log("🧪 Testing setup...");

try {
    // Test if BAML client was generated
    const fs = require('fs');
    const path = require('path');
    
    const bamlClientPath = path.join(__dirname, 'baml_client');
    if (!fs.existsSync(bamlClientPath)) {
        console.error("❌ BAML client not found. Run: npx baml-cli generate");
        process.exit(1);
    }
    console.log("✅ BAML client generated");
    
    // Test if we can import the client (skip for now since it's TypeScript)
    console.log("✅ BAML client structure looks good");
    
    // Test if chalk is available
    const chalk = require('chalk');
    console.log("✅ Chalk dependency available");
    
    // Test if TypeScript compilation works
    const { spawn } = require('child_process');
    const tsc = spawn('npx', ['tsc', '--noEmit'], { stdio: 'pipe' });
    
    tsc.on('close', (code) => {
        if (code === 0) {
            console.log("✅ TypeScript compilation successful");
            console.log("");
            console.log("🎉 Setup verification complete!");
            console.log("");
            console.log("Next steps:");
            console.log("1. Add your OPENAI_API_KEY to the .env file");
            console.log("2. Run: npm run dev");
            console.log("3. Try: 'multiply 5 and 7'");
        } else {
            console.error("❌ TypeScript compilation failed");
            process.exit(1);
        }
    });
    
} catch (error) {
    console.error("❌ Setup test failed:", error.message);
    console.log("");
    console.log("Try running the setup script: ./setup.sh");
    process.exit(1);
}
