#!/usr/bin/env node

// Test script to send a direct message through HumanLayer
require('dotenv').config();
const { humanlayer } = require('humanlayer');

async function testSlackDM() {
    console.log("💬 Testing Slack DM through HumanLayer...");
    
    // First, let's get the user list to find your user ID
    const token = process.env.HUMANLAYER_SLACK_BOT_TOKEN;
    
    try {
        console.log("🔍 Finding your user ID...");
        
        const response = await fetch('https://slack.com/api/users.list', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        
        if (!data.ok) {
            console.error("❌ Slack API error:", data.error);
            return;
        }

        // Look for users (excluding bots)
        const realUsers = data.members.filter(user => !user.is_bot && !user.deleted);
        
        console.log("👥 Available users for DM:");
        realUsers.forEach(user => {
            console.log(`   ${user.real_name || user.name} (@${user.name}) - ID: ${user.id}`);
        });

        // Try to send to the first real user (probably you)
        if (realUsers.length > 0) {
            const targetUser = realUsers[0];
            console.log(`\n📤 Sending DM to: ${targetUser.real_name || targetUser.name} (${targetUser.id})`);

            const hl = humanlayer({
                apiKey: process.env.HUMANLAYER_API_KEY,
                runId: `slack-dm-test-${Date.now()}`,
                contactChannel: {
                    slack: {
                        channel_or_user_id: targetUser.id,
                        context_about_channel_or_user: `direct message to ${targetUser.real_name || targetUser.name}`
                    }
                }
            });

            const response = await hl.createHumanContact({
                spec: {
                    msg: "🧪 **Test DM from Claims System**\n\nThis is a test direct message to verify Slack integration is working!\n\n**Test Details:**\n- System: Claims Processing\n- Purpose: Slack DM verification\n- Time: " + new Date().toISOString() + "\n\nPlease approve this test! 🚀",
                    state: {
                        test_id: `slack-dm-test-${Date.now()}`,
                        purpose: "slack_dm_verification",
                        user_id: targetUser.id
                    }
                }
            });

            console.log("✅ Slack DM sent successfully!");
            console.log("📋 Response details:", {
                status: response.status,
                approved: response.approved,
                comment: response.comment
            });

        } else {
            console.log("❌ No users found to send DM to");
        }

    } catch (error) {
        console.error("❌ Error sending Slack DM:", error);
    }
}

testSlackDM();
