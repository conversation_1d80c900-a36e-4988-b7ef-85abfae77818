#!/usr/bin/env node

// Test script to verify HumanLayer configuration
require('dotenv').config();

console.log("🧪 Testing HumanLayer configuration...");

// Check required environment variables
const requiredVars = ['OPENAI_API_KEY', 'HUMANLAYER_API_KEY'];
const optionalVars = ['HUMANLAYER_EMAIL_ADDRESS', 'HUMANLAYER_SLACK_CHANNEL_ID'];

let missingRequired = [];
let hasContactChannel = false;

// Check required variables
requiredVars.forEach(varName => {
    if (!process.env[varName]) {
        missingRequired.push(varName);
    } else {
        console.log(`✅ ${varName} is set`);
    }
});

// Check contact channels
optionalVars.forEach(varName => {
    if (process.env[varName]) {
        console.log(`✅ ${varName} is set`);
        hasContactChannel = true;
    }
});

if (missingRequired.length > 0) {
    console.error("❌ Missing required environment variables:");
    missingRequired.forEach(varName => {
        console.error(`   - ${varName}`);
    });
    console.log("");
    console.log("Please add these to your .env file:");
    console.log("1. Get OpenAI API key from: https://platform.openai.com/api-keys");
    console.log("2. Get HumanLayer API key from: https://app.humanlayer.dev");
    process.exit(1);
}

if (!hasContactChannel) {
    console.warn("⚠️  No contact channel configured!");
    console.log("Add one of these to your .env file:");
    console.log("   - HUMANLAYER_EMAIL_ADDRESS=<EMAIL>");
    console.log("   - HUMANLAYER_SLACK_CHANNEL_ID=C1234567890");
    console.log("");
    console.log("Without a contact channel, approvals will only work in CLI mode.");
}

console.log("");
console.log("🎉 Configuration looks good!");
console.log("");
console.log("Next steps:");
console.log("1. Run: npm run dev");
console.log("2. Try: 'divide 10 by 2' (requires approval)");
console.log("3. Check your email/Slack for approval request");
console.log("");

if (hasContactChannel) {
    console.log("📧 When you test division or refunds, you should receive:");
    if (process.env.HUMANLAYER_EMAIL_ADDRESS) {
        console.log(`   - Email at: ${process.env.HUMANLAYER_EMAIL_ADDRESS}`);
    }
    if (process.env.HUMANLAYER_SLACK_CHANNEL_ID) {
        console.log(`   - Slack message in channel: ${process.env.HUMANLAYER_SLACK_CHANNEL_ID}`);
    }
    console.log("   - Approve/Reject buttons to respond");
}
