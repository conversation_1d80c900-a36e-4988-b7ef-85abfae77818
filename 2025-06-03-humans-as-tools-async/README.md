# Humans as Tools: Async Agents and Durable Execution

[Video](https://youtu.be/NMhH5_ju3-I)

<a href="https://www.youtube.com/watch?v=NMhH5_ju3-I"><img width="600" alt="Screenshot 2025-06-10 at 8 56 45 AM" src="https://github.com/user-attachments/assets/1c01a45f-0103-43fd-98cd-4e2adb59c04f" /></a>

This session builds on our [12-factor agents workshop](../2025-04-22-twelve-factor-agents) to explore async agents and durable execution patterns. We'll learn how to build agents that can pause, contact humans for feedback or approval, and resume execution based on human responses.

## What This Agent Does

This is a **human-in-the-loop AI agent** that demonstrates async execution patterns. The agent can:

- 🧮 **Perform math operations** (add, subtract, multiply, divide)
- 💰 **Process customer refunds** (requires human approval)
- ❓ **Request clarification** from humans when needed
- ⏸️ **Pause and resume** execution based on human input
- 💾 **Persist state** across sessions using file system storage

### Key Features

- **Deterministic approval**: Some operations (division, refunds) always require human approval
- **Non-deterministic approval**: Agent can choose to ask for clarification when confused
- **Multiple interfaces**: CLI and HTTP server modes
- **Durable execution**: Conversations persist and can be resumed
- **HumanLayer integration**: Optional email/Slack notifications for approvals

## What You'll Learn

- How to implement async agent patterns with human-in-the-loop workflows
- State management for durable agent execution
- Different channels for human interaction (CLI, HTTP, email)
- Webhook integration for non-blocking human approvals
- Testing strategies for async agent workflows

## Key Takeaways

- Two types of human interaction - deterministic (code enforces human approval) and non-deterministic (agent chooses to contact a human)
- approver might not be the person interacting with the chatbot
- State management is key to building agents that can pause/resume for human interaction
- Separate concerns of inner loop (agent) and outer loop (human interaction)

## Prerequisites

- Basic TypeScript knowledge
- Node.js 20+ installed
- Understanding of async/await patterns
- Familiarity with HTTP APIs and webhooks
- OpenAI API key (required) or Anthropic API key (optional)

## Quick Setup

### Option 1: Automated Setup
```bash
# Run the setup script
./setup.sh
```

### Option 2: Manual Setup
```bash
# Install dependencies
npm install
npm install chalk

# Generate BAML client
npx baml-cli generate

# Copy environment template
cp .env.example .env

# Edit .env and add your API keys
# OPENAI_API_KEY=your_key_here
```

### Get API Keys
- **OpenAI**: Get your API key from [platform.openai.com/api-keys](https://platform.openai.com/api-keys)
- **Anthropic** (optional): Get your API key from [console.anthropic.com](https://console.anthropic.com)

## Running the Agent

### CLI Mode (Interactive)
```bash
# Start with default message
npm run dev

# Or start with a custom message
npx tsx src/index.ts "multiply 5 and 7"
```

### HTTP Server Mode
```bash
# Start the server
npx tsx src/server.ts

# Then make requests to http://localhost:3000
```

## Example Interactions

### Basic Math (No Approval Required)
```
User: "multiply 5 and 7"
Agent: Performs calculation → 35
```

### Division (Requires Approval)
```
User: "divide 10 by 2"
Agent: Requests approval → Human approves → Result: 5
```

### Refund Processing (Requires Approval)
```
User: "process a refund for order 12345 for $50 because item was damaged"
Agent: Requests approval → Human approves → Refund processed
```

### Clarification Request
```
User: "multiply 3 and xyz123"
Agent: "Could you please clarify the second number?"
User: "I meant 4"
Agent: Performs calculation → 12
```

## Architecture Overview

### Inner vs Outer Loop
- **Inner Loop**: The agent's reasoning and tool execution
- **Outer Loop**: Human interaction and approval workflows

### State Management
- Conversations are stored as `Thread` objects
- Each thread contains a sequence of `Event` objects
- State persists to filesystem for durability
- Threads can be paused and resumed

### Human Interaction Types
1. **Deterministic**: Code enforces human approval (division, refunds)
2. **Non-deterministic**: Agent chooses to ask for help (clarification)

## Whiteboards

### inner vs outer loop

![image](https://github.com/user-attachments/assets/3f3269f1-e177-473f-a4bc-7802255447dc)


### deterministic vs non-deterministic human approval

![image](https://github.com/user-attachments/assets/a36a19ec-52fa-43d1-be02-63cbf209d11e)


### base agent architecture refresh

![image](https://github.com/user-attachments/assets/b11a5c94-b1a0-4d02-89fb-9640ce436484)


![image](https://github.com/user-attachments/assets/661500e9-ba0e-496e-a774-e0add0d2b8e6)


![image](https://github.com/user-attachments/assets/d54415a4-5452-4035-8cf8-70b13ef3dafd)

## Testing

### Run BAML Tests
```bash
# Run all tests
npx baml-cli test

# Run specific tests
npx baml-cli test -i "HelloWorld"
npx baml-cli test -i "MathOperation*"
```

### Test the Agent
```bash
# Test basic functionality
npx tsx src/index.ts "add 2 and 3"

# Test approval workflow
npx tsx src/index.ts "divide 10 by 2"

# Test clarification
npx tsx src/index.ts "multiply 5 and abc"
```

## Configuration

### Environment Variables
- `OPENAI_API_KEY`: Required for GPT models
- `ANTHROPIC_API_KEY`: Optional for Claude models
- `HUMANLAYER_EMAIL_ADDRESS`: Optional for email notifications
- `HUMANLAYER_SLACK_CHANNEL_ID`: Optional for Slack notifications

### BAML Configuration
- `baml_src/clients.baml`: AI model configurations
- `baml_src/agent.baml`: Agent behavior and tools
- `baml_src/generators.baml`: Code generation settings

## Troubleshooting

### Common Issues

1. **"Cannot find module '../baml_client'"**
   ```bash
   npx baml-cli generate
   ```

2. **"OPENAI_API_KEY not set"**
   - Add your API key to the `.env` file
   - Get a key from [platform.openai.com/api-keys](https://platform.openai.com/api-keys)

3. **"chalk module not found"**
   ```bash
   npm install chalk
   ```

### Debug Mode
Set environment variable for verbose logging:
```bash
DEBUG=* npx tsx src/index.ts
```

## Next Steps

1. **Extend the agent**: Add more tools and capabilities
2. **Implement webhooks**: For non-blocking approvals
3. **Add persistence**: Use a database instead of filesystem
4. **Scale horizontally**: Deploy multiple agent instances
5. **Add monitoring**: Track agent performance and human interactions

## Learn More

- [BAML Documentation](https://docs.boundaryml.com/)
- [HumanLayer Documentation](https://humanlayer.dev/)
- [12-Factor Agents Workshop](../2025-04-22-twelve-factor-agents)
- [Original Video Tutorial](https://youtu.be/NMhH5_ju3-I)
