#!/usr/bin/env node

// <PERSON>ript to list Slack channels and get their IDs
require('dotenv').config();

async function getSlackChannels() {
    const token = process.env.HUMANLAYER_SLACK_BOT_TOKEN;
    
    if (!token) {
        console.error("❌ HUMANLAYER_SLACK_BOT_TOKEN not found in .env file");
        return;
    }

    console.log("🔍 Fetching Slack channels...");

    try {
        const response = await fetch('https://slack.com/api/conversations.list', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (!data.ok) {
            console.error("❌ Slack API error:", data.error);
            if (data.error === 'invalid_auth') {
                console.log("💡 Check your HUMANLAYER_SLACK_BOT_TOKEN - it might be invalid");
            }
            return;
        }

        console.log("📋 Available channels:");
        console.log("");

        data.channels.forEach(channel => {
            const type = channel.is_private ? '🔒 Private' : '📢 Public';
            const member = channel.is_member ? '✅ Bot is member' : '❌ Bot not member';
            
            console.log(`${type} #${channel.name}`);
            console.log(`   ID: ${channel.id}`);
            console.log(`   ${member}`);
            console.log(`   Purpose: ${channel.purpose?.value || 'No purpose set'}`);
            console.log("");
        });

        console.log("💡 To use a channel for claims notifications:");
        console.log("1. Copy the channel ID (starts with C)");
        console.log("2. Update CLAIMS_SLACK_CHANNEL in your .env file");
        console.log("3. Make sure the bot is a member of the channel");

    } catch (error) {
        console.error("❌ Error fetching channels:", error.message);
    }
}

getSlackChannels();
