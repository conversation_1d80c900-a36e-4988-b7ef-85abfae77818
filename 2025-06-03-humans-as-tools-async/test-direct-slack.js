#!/usr/bin/env node

// Test script to send a message directly via Slack API (bypassing Human<PERSON>ayer)
require('dotenv').config();

async function testDirectSlackMessage() {
    const token = process.env.HUMANLAYER_SLACK_BOT_TOKEN;
    const channel = process.env.CLAIMS_SLACK_CHANNEL;
    
    console.log("🚀 Testing direct Slack API message...");
    console.log(`📤 Sending to channel: ${channel}`);
    
    try {
        const response = await fetch('https://slack.com/api/chat.postMessage', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                channel: channel,
                text: "🧪 **Direct Slack API Test**\n\nThis message is sent directly via Slack API to test if the bot can post messages to the #claim-liability-approvals channel.\n\n✅ If you see this message, the bot has the right permissions!\n\n🔧 Next step: Fix HumanLayer integration",
                blocks: [
                    {
                        type: "section",
                        text: {
                            type: "mrkdwn",
                            text: "🧪 *Direct Slack API Test*\n\nThis message is sent directly via Slack API to test if the bot can post messages to the #claim-liability-approvals channel."
                        }
                    },
                    {
                        type: "section",
                        text: {
                            type: "mrkdwn",
                            text: "✅ *If you see this message, the bot has the right permissions!*\n\n🔧 *Next step:* Fix HumanLayer integration"
                        }
                    },
                    {
                        type: "actions",
                        elements: [
                            {
                                type: "button",
                                text: {
                                    type: "plain_text",
                                    text: "✅ Bot Works!"
                                },
                                style: "primary",
                                value: "bot_test_success"
                            },
                            {
                                type: "button",
                                text: {
                                    type: "plain_text",
                                    text: "❌ Issues"
                                },
                                style: "danger",
                                value: "bot_test_failure"
                            }
                        ]
                    }
                ]
            })
        });

        const data = await response.json();

        if (data.ok) {
            console.log("✅ Message sent successfully!");
            console.log(`📋 Message timestamp: ${data.ts}`);
            console.log(`📍 Channel: ${data.channel}`);
            console.log("");
            console.log("🎉 The bot CAN send messages to Slack!");
            console.log("🔧 The issue is with HumanLayer integration, not Slack permissions.");
        } else {
            console.error("❌ Slack API error:", data.error);
            
            if (data.error === 'channel_not_found') {
                console.log("💡 The channel ID might be wrong or the bot isn't in the channel");
            } else if (data.error === 'not_in_channel') {
                console.log("💡 The bot needs to be added to the channel");
            } else if (data.error === 'invalid_auth') {
                console.log("💡 Check your HUMANLAYER_SLACK_BOT_TOKEN");
            }
        }

    } catch (error) {
        console.error("❌ Error sending message:", error.message);
    }
}

testDirectSlackMessage();
