{"name": "my-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "tsx src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext .ts", "test": "jest", "walkthrough": "tsx hack/run-walkthrough.ts", "walkthrough:interactive": "tsx hack/run-walkthrough.ts -i", "walkthrough:diff": "tsx hack/run-walkthrough.ts -d", "walkthrough:interactive-diff": "tsx hack/run-walkthrough.ts -i -d"}, "dependencies": {"@boundaryml/baml": "^0.84.4", "baml": "^0.0.0", "express": "^4.21.2", "tsx": "^4.15.0", "typescript": "^5.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "chalk": "^5.4.1", "eslint": "^8.0.0", "jest": "^29.0.0", "supertest": "^6.3.4", "ts-jest": "^29.0.0"}}