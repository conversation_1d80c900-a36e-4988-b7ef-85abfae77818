
enum EntityType {
	Individual
	Corporation
	Charity
	Other 
	Unknown
}


class NotAGiftEmail {
	type "not_a_gift_email"
	reasoning string
}

class GiftEmailAnalysis {
	type "gift_received" | "gift_given"
	sender string
	sender_relationship string @description("The relationship between the sender and the company")
	sender_entity_type EntityType
	recipient string
	recipient_relationship string @description("The relationship between the recipient and the company")
	recipient_entity_type EntityType
	risk_level "low" | "medium" | "high"
	reasoning string
	open_questions string[] @description("A list of questions that are relevant to the email")
	follow_up_actions string[] @description("A description of the next steps to take to answer any open questions")
}

// Create a function to extract the resume from a string.
function EvaluateGiftPolicy(email: string, company_name: string) -> NotAGiftEmail | GiftEmailAnalysis {
  // Specify a client as provider/model-name
  client "openai/gpt-4o" // Set OPENAI_API_KEY to use this client.
  prompt #"

	You are a compliance expert working at {{ company_name }}.


    Your goal is to determine whether the email
    evidence violates the policy.

	In this case, the policy is:


	Members must not accept gifts or favors from any person or entity that is a subject of the Company's business, including suppliers, customers, competitors, or other third parties.
    {{ ctx.output_format }}

    {{ _.role("user") }}

    <email>
    {{ email }}
    </email>
  "#
}


test evaluate_gift_policy_1 {
  functions [EvaluateGiftPolicy]


  args {
    company_name "Enron"

    email #"
      Message-ID: <7228326.1075840095747.JavaMail.evans@thyme>
Date: Wed, 13 Dec 2000 10:04:00 -0800 (PST)
From: <EMAIL>
To: <EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>, 
	<EMAIL>, <EMAIL>
Subject: Thank you for the Charitygift
Mime-Version: 1.0
Content-Type: text/plain; charset=us-ascii
Content-Transfer-Encoding: 7bit
X-From: Rosalee Fleming
X-To: James M Bannantine, Cliff Baxter, Sanjay Bhatnagar, Jeremy Blachman, Philippe A Bibi, Raymond Bowen, Michael R Brown, Harold G Buchanan, Rick Buy, Richard Causey, Diomedes Christodoulou, Wade Cline, David Cox, David W Delainey, James Derrick, Steve Elliott, Jim Fallon, Andrew S Fastow, Mark Frevert, Ben F Glisan, Kevin Hannon, David Haug, Rod Hayslett, Stanley Horton, James A Hughes, Larry L Izzo, Steven J Kean, Louise Kitchen, Mark Koenig, Kenneth Lay, John J Lavorato, Dan Leff, Danny McCarty, Mike McConnell, Rebecca McDonald, Jeffrey McMahon, Mark Metts, Mark S Muller, Cindy Olson, Lou L Pai, Ken Rice, Matthew Scrimshaw, Jeffrey A Shankman, Jeffrey Sherrick, John Sherriff, Jeff Skilling, Marty Sunde, Greg Whalley, Thomas E White, G G Garcia, Marcia Manarin, Susan Skarness, Stacy Guidroz, Beena Pradhan, Karen K Heathman, Sharron Westbrook, Kay Chapman, Molly Bobrow, Rosane Fabozzi, Stephanie Harris, Bridget Maronge, Nicki Daw, Inez Dauterive, Carol Ann Brown, Elaine Rodriguez, Cindy Stark, Mary E Garza, Maureen McVicker, Joannie Williamson, Vanessa Groscrand, Suzanne Danz, Tori L Wells, Cathy Phillips, Loretta Brelsford, Sue Ford, Dolores Fisher, Kathy McMahon, Karen Owens, Dorothy Dalton, Mercedes Estrada, Christina Grow, Lauren Urquhart, Sherri Sera, Katherine Brown, Liz M Taylor, Judy G Smith, Peggy McCurley, Marsha Schiller, Fiona Stewart, Jana L Paxton, Connie Blackwood, Tammie Schoppe, Kimberly Hillis, Jennifer Burns, Sharon Dick, Beverly Aden, Kathy Dodgen, Kerry Ferrari, Carol Moffett, Jennifer Adams, Leah Rijo, Lucy Marshall, Kathy Campos, Julie Armstrong, Kathryn Greer, Mrudula Gadade, Brenda Castillo
X-cc: 
X-bcc: 
X-Folder: \Jeffrey_Skilling_Dec2000\Notes Folders\Notes inbox
X-Origin: SKILLING-J
X-FileName: jskillin.nsf

---------------------- Forwarded by Rosalee Fleming/Corp/Enron on 12/13/2000 
05:59 PM ---------------------------

Kathy Mayfield
12/13/2000 05:02 PM


To: Rosalee Fleming/Corp/Enron@ENRON
cc:  
Subject: Thank you for the Charitygift


---------------------- Forwarded by Kathy Mayfield/Corp/Enron on 12/13/2000 
04:38 PM ---------------------------


<EMAIL> on 12/13/2000 04:34:58 PM
To: <EMAIL>
cc:  

Subject: Thank you for the Charitygift


Thank you for the Charity Gift Card.  I decided to donate the gift to the 
Depelchin Children's Center.


 "#
  }
}