version = 1
requires-python = ">=3.13"

[[package]]
name = "2025-04-15-code-generation-small-models"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "baml-py" },
    { name = "pytest" },
]

[package.metadata]
requires-dist = [
    { name = "baml-py", specifier = ">=0.83.0" },
    { name = "pytest", specifier = ">=8.3.5" },
]

[[package]]
name = "baml-py"
version = "0.83.0"
source = { registry = "https://pypi.org/simple" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9e/2c/9840181d0b45982c17c9133552731a30da4e398ba9dffd6a63e1ffd78eb9/baml_py-0.83.0-cp38-abi3-macosx_10_12_x86_64.whl", hash = "sha256:fb5fd3cb5cb83f5e785c9e14b1df940c54a1ed7efa2199e1a2550de3efbfb0d2", size = 13441210 },
    { url = "https://files.pythonhosted.org/packages/11/eb/756bb911137ae5dc85ea19a9c79b8e553580fad9538544e4613baf4368f9/baml_py-0.83.0-cp38-abi3-macosx_11_0_arm64.whl", hash = "sha256:ff0bd7a502ac5b5187b51e422896eb0e4a0b86c8fb4f3c4c8a30029485533919", size = 12571703 },
    { url = "https://files.pythonhosted.org/packages/b4/f1/82f53216d254514dd101e96100d4952e57354133f4328afcde5f58ccb900/baml_py-0.83.0-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:cf2874805dc9f0f787e592c42d85e38461dd59f7b4f890a5cd38e52489752105", size = 15792574 },
    { url = "https://files.pythonhosted.org/packages/e5/aa/2994b774fab154740816d75fed39209b5215ea5307d788db1fc5c11cf150/baml_py-0.83.0-cp38-abi3-manylinux_2_24_aarch64.whl", hash = "sha256:804df7a8ff7d8f459b4355a9e387867e0a78c7994839b576436fa9e21c96cd34", size = 15355735 },
    { url = "https://files.pythonhosted.org/packages/9f/09/ba6c2bf284d2996e13f91ffa0f92f5c48909c6921b5f834fc461b366328f/baml_py-0.83.0-cp38-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:8627026852102f5f0a852ae33b92c02983fd7a5f054e473ecaf313e2f90fa33d", size = 15624997 },
    { url = "https://files.pythonhosted.org/packages/49/00/a8cc50dac0261c83682f34760d05c72e48ad89736d3ac8e83f5ef8f835b2/baml_py-0.83.0-cp38-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:bf0fcf8fef5b6184634f640fa0906de79684ee2093e3dda177d3df35b6a6a8a5", size = 15948890 },
    { url = "https://files.pythonhosted.org/packages/38/72/7ebf81de74db4d262de8628c4614c5bdba252f9a52d59aa0c1d30a87d8b9/baml_py-0.83.0-cp38-abi3-win_amd64.whl", hash = "sha256:c1601d9fc0c5d611b87e1c7c62cff81469fec1073cf62a2fb43846f9a0815acb", size = 13579581 },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d8/53/6f443c9a4a8358a93a6792e2acffb9d9d5cb0a5cfd8802644b7b1c9a02e4/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44", size = 27697 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6", size = 25335 },
]

[[package]]
name = "iniconfig"
version = "2.1.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/f2/97/ebf4da567aa6827c909642694d71c9fcf53e5b504f2d96afea02718862f3/iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7", size = 4793 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760", size = 6050 },
]

[[package]]
name = "packaging"
version = "24.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d0/63/68dbb6eb2de9cb10ee4c9c14a0148804425e13c4fb20d61cce69f53106da/packaging-24.2.tar.gz", hash = "sha256:c228a6dc5e932d346bc5739379109d49e8853dd8223571c7c5b55260edc0b97f", size = 163950 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/88/ef/eb23f262cca3c0c4eb7ab1933c3b1f03d021f2c48f54763065b6f0e321be/packaging-24.2-py3-none-any.whl", hash = "sha256:09abb1bccd265c01f4a3aa3f7a7db064b36514d2cba19a2f694fe6150451a759", size = 65451 },
]

[[package]]
name = "pluggy"
version = "1.5.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/96/2d/02d4312c973c6050a18b314a5ad0b3210edb65a906f868e31c111dede4a6/pluggy-1.5.0.tar.gz", hash = "sha256:2cffa88e94fdc978c4c574f15f9e59b7f4201d439195c3715ca9e2486f1d0cf1", size = 67955 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/88/5f/e351af9a41f866ac3f1fac4ca0613908d9a41741cfcf2228f4ad853b697d/pluggy-1.5.0-py3-none-any.whl", hash = "sha256:44e1ad92c8ca002de6377e165f3e0f1be63266ab4d554740532335b9d75ea669", size = 20556 },
]

[[package]]
name = "pytest"
version = "8.3.5"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "iniconfig" },
    { name = "packaging" },
    { name = "pluggy" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ae/3c/c9d525a414d506893f0cd8a8d0de7706446213181570cdbd766691164e40/pytest-8.3.5.tar.gz", hash = "sha256:f4efe70cc14e511565ac476b57c279e12a855b11f48f212af1080ef2263d3845", size = 1450891 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/30/3d/64ad57c803f1fa1e963a7946b6e0fea4a70df53c1a7fed304586539c2bac/pytest-8.3.5-py3-none-any.whl", hash = "sha256:c69214aa47deac29fad6c2a4f590b9c4a9fdb16a403176fe154b79c0b4d4d820", size = 343634 },
]
