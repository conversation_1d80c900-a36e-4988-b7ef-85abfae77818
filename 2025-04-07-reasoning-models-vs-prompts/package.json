{"name": "2025-04-07-reasoning-models-vs-prompts", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev ", "build": "npm run generate && next build", "start": "npm run generate && next start", "lint": "next lint", "generate": "baml-cli generate"}, "dependencies": {"@boundaryml/baml": "^0.82.0", "dotenv": "^16.4.7", "neo4j-driver": "^5.28.1", "next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@boundaryml/baml-nextjs-plugin": "^0.1.0", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}