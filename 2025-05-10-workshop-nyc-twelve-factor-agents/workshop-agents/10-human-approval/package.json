{"name": "my-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "tsx src/index.ts", "build": "tsc"}, "dependencies": {"@boundaryml/baml": "0.87.2", "express": "^5.1.0", "tsx": "^4.15.0", "typescript": "^5.0.0"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "supertest": "^7.1.0"}}